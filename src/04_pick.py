import os, json, requests, dotenv; dotenv.load_dotenv()
OR=os.getenv("OPENROUTER_API_KEY")

PROMPT="""Return JSON list (max 2) of {{start,end}} seconds (<30s) that are most engaging.
transcript={txt}
shots={shots}
"""

def pick():
    txt=open("transcript.json").read()[:2000]
    shots=open("shots.json").read()[:500]
    payload={"model":"openai/gpt-4o-mini",
             "messages":[{"role":"user","content":PROMPT.format(txt=txt,shots=shots)}]}
    r=requests.post("https://openrouter.ai/api/v1/chat/completions",
        headers={"Authorization":f"Bearer {OR}","HTTP-Referer":"https://example.com"},
        json=payload,timeout=60).json()
    return json.loads(r["choices"][0]["message"]["content"])

if __name__=="__main__":
    json.dump(pick(),open("clips.json","w"),indent=2)
