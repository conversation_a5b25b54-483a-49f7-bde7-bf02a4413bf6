import base64, json, os, requests, time, sys, subprocess, dotenv; dotenv.load_dotenv()
KEY=os.getenv("SHOTSTACK_API_KEY")

def render_with_ffmpeg(src: str, clips: list) -> str:
    """使用 ffmpeg 本地渲染视频片段"""
    print("使用 ffmpeg 进行本地渲染...")

    output_files = []

    # 为每个片段创建单独的视频文件
    for i, clip in enumerate(clips):
        start_time = clip["start"]
        duration = clip["end"] - clip["start"]
        output_file = f"clip_{i+1}.mp4"

        print(f"提取片段 {i+1}: {start_time:.2f}s - {clip['end']:.2f}s (时长: {duration:.2f}s)")

        # 使用 ffmpeg 提取片段
        cmd = [
            "ffmpeg", "-y",  # -y 覆盖输出文件
            "-i", src,
            "-ss", str(start_time),
            "-t", str(duration),
            "-c", "copy",  # 复制编码，避免重新编码
            output_file
        ]

        try:
            subprocess.run(cmd, capture_output=True, text=True, check=True)
            output_files.append(output_file)
            print(f"片段 {i+1} 提取成功: {output_file}")
        except subprocess.CalledProcessError as e:
            print(f"ffmpeg 错误: {e}")
            print(f"stderr: {e.stderr}")
            raise RuntimeError(f"无法提取片段 {i+1}")

    # 如果有多个片段，将它们合并
    if len(output_files) > 1:
        final_output = "final_clips.mp4"
        print(f"合并 {len(output_files)} 个片段...")

        # 创建文件列表
        with open("filelist.txt", "w") as f:
            for file in output_files:
                f.write(f"file '{file}'\n")

        # 使用 ffmpeg 合并视频
        cmd = [
            "ffmpeg", "-y",
            "-f", "concat",
            "-safe", "0",
            "-i", "filelist.txt",
            "-c", "copy",
            final_output
        ]

        try:
            subprocess.run(cmd, capture_output=True, text=True, check=True)
            print(f"视频合并成功: {final_output}")

            # 清理临时文件
            for file in output_files:
                os.remove(file)
            os.remove("filelist.txt")

            return os.path.abspath(final_output)
        except subprocess.CalledProcessError as e:
            print(f"ffmpeg 合并错误: {e}")
            print(f"stderr: {e.stderr}")
            raise RuntimeError("无法合并视频片段")
    else:
        # 只有一个片段，直接返回
        return os.path.abspath(output_files[0])

def render(src:str, clips:list)->str:
    print(f"开始渲染，源文件: {src}")
    print(f"片段数量: {len(clips)}")

    # 检查文件是否存在
    if not os.path.exists(src):
        raise FileNotFoundError(f"源文件不存在: {src}")

    # 检查 API 密钥
    if not KEY:
        raise ValueError("SHOTSTACK_API_KEY 未设置")

    # 检查文件大小
    file_size = os.path.getsize(src)
    print(f"视频文件大小: {file_size / (1024*1024):.2f} MB")

    # 如果文件太大，我们需要使用不同的方法
    if file_size > 8 * 1024 * 1024:  # 8MB 限制
        print("文件太大，无法直接上传到 Shotstack API")
        print("建议使用以下方法之一:")
        print("1. 压缩视频文件")
        print("2. 将视频上传到云存储并使用 URL")
        print("3. 使用本地视频编辑工具")

        # 作为替代方案，我们可以使用 ffmpeg 来本地处理
        return render_with_ffmpeg(src, clips)

    print("正在编码视频文件...")
    enc="data:video/mp4;base64,"+base64.b64encode(open(src,"rb").read()).decode()
    print(f"视频编码完成，大小: {len(enc)} 字符")

    tmpl={"timeline":{"tracks":[{"clips":[]}]},
          "output":{"format":"mp4","resolution":"1080x1920","fps":30}}

    for i, c in enumerate(clips):
        print(f"添加片段 {i+1}: {c['start']:.2f}s - {c['end']:.2f}s")
        tmpl["timeline"]["tracks"][0]["clips"].append({
            "asset":{"type":"video","src":enc},
            "start":0,"length":c["end"]-c["start"],
            "trim":{"start":c["start"]},
            "fit":"cover"})

    print("发送渲染请求...")
    response = requests.post("https://api.shotstack.io/v1/render",
        headers={"x-api-key":KEY},json=tmpl)

    print(f"HTTP 状态码: {response.status_code}")
    print(f"响应内容: {response.text}")

    if response.status_code != 200 and response.status_code != 201:
        raise RuntimeError(f"API 请求失败: {response.status_code} - {response.text}")

    try:
        r = response.json()
    except json.JSONDecodeError:
        raise RuntimeError(f"无法解析 API 响应: {response.text}")

    if "response" not in r or "id" not in r["response"]:
        raise RuntimeError(f"API 响应格式错误: {r}")

    rid = r["response"]["id"]
    print(f"渲染任务 ID: {rid}")

    while True:
        print("检查渲染状态...")
        status_response = requests.get(f"https://api.shotstack.io/v1/render/{rid}",
            headers={"x-api-key":KEY})

        if status_response.status_code != 200:
            raise RuntimeError(f"状态检查失败: {status_response.status_code} - {status_response.text}")

        try:
            s = status_response.json()
        except json.JSONDecodeError:
            raise RuntimeError(f"无法解析状态响应: {status_response.text}")

        status = s["response"]["status"]
        print(f"当前状态: {status}")

        if status == "done":
            return s["response"]["url"]
        if status == "failed":
            raise RuntimeError(f"渲染失败: {s}")

        time.sleep(8)

if __name__=="__main__":
    clips=json.load(open("clips.json"))
    print(render(sys.argv[1], clips))
