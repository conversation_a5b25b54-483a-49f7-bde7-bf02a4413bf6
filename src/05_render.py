import base64, json, os, requests, time, sys, dotenv; dotenv.load_dotenv()
KEY=os.getenv("SHOTSTACK_API_KEY")

def render(src:str, clips:list)->str:
    enc="data:video/mp4;base64,"+base64.b64encode(open(src,"rb").read()).decode()
    tmpl={"timeline":{"tracks":[{"clips":[]}]},
          "output":{"format":"mp4","resolution":"1080x1920","fps":30}}
    for c in clips:
        tmpl["timeline"]["tracks"][0]["clips"].append({
            "asset":{"type":"video","src":enc},
            "start":0,"length":c["end"]-c["start"],
            "trim":{"start":c["start"]},
            "fit":"cover"})
    r=requests.post("https://api.shotstack.io/v1/render",
        headers={"x-api-key":KEY},json=tmpl).json()
    rid=r["response"]["id"]
    while True:
        s=requests.get(f"https://api.shotstack.io/v1/render/{rid}",
            headers={"x-api-key":KEY}).json()
        if s["response"]["status"]=="done":
            return s["response"]["url"]
        if s["response"]["status"]=="failed":
            raise RuntimeError(s)
        time.sleep(8)

if __name__=="__main__":
    clips=json.load(open("clips.json"))
    print(render(sys.argv[1], clips))
