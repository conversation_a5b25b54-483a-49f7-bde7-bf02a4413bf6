import subprocess, pathlib, sys, yt_dlp, tqdm

def download_video(link: str) -> str:
    out = pathlib.Path("demo.mp4").resolve()
    bar = tqdm.tqdm(total=100, desc="Downloading")
    yd = yt_dlp.YoutubeDL({"outtmpl": str(out),
                           "progress_hooks":[lambda d:
                               bar.update(d.get("_percent_str",0).replace('%',''))]})
    yd.download([link])
    bar.close()
    return str(out)

if __name__ == "__main__":
    print(download_video(sys.argv[1]))
